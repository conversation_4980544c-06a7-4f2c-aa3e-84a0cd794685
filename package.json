{"name": "saran-portfolio-ai", "version": "2.0.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build && npm run optimize", "build:analyze": "ANALYZE=true next build", "build:production": "NODE_ENV=production next build && npm run optimize", "optimize": "node scripts/build-optimize.js", "security-headers": "node scripts/generate-security-headers.js", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "security-check": "npm audit --audit-level moderate", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:build": "npm run build && npm run start", "clean": "rm -rf .next out node_modules/.cache", "precommit": "npm run lint && npm run type-check && npm run test", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "lighthouse": "lighthouse http://localhost:3000 --output=json --output-path=./lighthouse-report.json", "bundle-analyzer": "cross-env ANALYZE=true npm run build"}, "dependencies": {"@vercel/analytics": "^1.4.1", "@vercel/speed-insights": "^1.1.0", "critters": "^0.0.23", "dompurify": "^3.2.2", "framer-motion": "^11.15.0", "lucide-react": "^0.468.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-intersection-observer": "^9.14.0", "validator": "^13.12.0", "zustand": "^5.0.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^15.3.2", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/node": "^20.17.56", "@types/react": "^19", "@types/react-dom": "^19", "@types/validator": "^13.12.2", "@types/webpack": "^5.28.5", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lighthouse": "^12.2.1", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^4", "typescript": "^5"}}