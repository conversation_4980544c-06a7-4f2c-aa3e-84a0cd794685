import {
  sanitizeInput,
  sanitizeHTML,
  validateEmail,
  validateName,
  validateMessage,
  escapeRegExp,
  generateCSRFToken,
  validateCSRFToken,
  hashString,
  isValidURL,
} from '../security';

// Mock DOMPurify
jest.mock('dompurify', () => ({
  sanitize: jest.fn((input) => input.replace(/<[^>]*>/g, '')),
}));

// Mock validator
jest.mock('validator', () => ({
  isEmail: jest.fn((email) => email.includes('@') && email.includes('.')),
  isURL: jest.fn((url) => url.startsWith('http')),
  escape: jest.fn((str) => str.replace(/[&<>"']/g, (match) => {
    const escapeMap: { [key: string]: string } = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#x27;',
    };
    return escapeMap[match];
  })),
}));

describe('security utilities', () => {
  describe('sanitizeInput', () => {
    it('removes HTML tags from input', () => {
      const input = '<script>alert("xss")</script>Hello World';
      const result = sanitizeInput(input);
      expect(result).toBe('Hello World');
    });

    it('handles empty input', () => {
      expect(sanitizeInput('')).toBe('');
    });

    it('handles input with only whitespace', () => {
      expect(sanitizeInput('   ')).toBe('');
    });

    it('preserves safe text content', () => {
      const input = 'This is safe text content';
      expect(sanitizeInput(input)).toBe(input);
    });
  });

  describe('sanitizeHTML', () => {
    it('allows safe HTML tags', () => {
      const html = '<p>Hello <strong>World</strong></p>';
      const result = sanitizeHTML(html);
      expect(result).toContain('<p>');
      expect(result).toContain('<strong>');
    });

    it('removes dangerous HTML tags', () => {
      const html = '<script>alert("xss")</script><p>Safe content</p>';
      const result = sanitizeHTML(html);
      expect(result).not.toContain('<script>');
      expect(result).toContain('Safe content');
    });
  });

  describe('validateEmail', () => {
    it('validates correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('rejects invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('test@')).toBe(false);
      expect(validateEmail('@example.com')).toBe(false);
    });

    it('rejects emails that are too long', () => {
      const longEmail = 'a'.repeat(250) + '@example.com';
      expect(validateEmail(longEmail)).toBe(false);
    });
  });

  describe('validateName', () => {
    it('validates correct names', () => {
      expect(validateName('John Doe')).toBe(true);
      expect(validateName('Jane Smith-Johnson')).toBe(true);
      expect(validateName("O'Connor")).toBe(true);
    });

    it('rejects invalid names', () => {
      expect(validateName('')).toBe(false);
      expect(validateName('A')).toBe(false); // Too short
      expect(validateName('A'.repeat(101))).toBe(false); // Too long
      expect(validateName('John123')).toBe(false); // Contains numbers
      expect(validateName('<script>')).toBe(false); // Contains HTML
    });
  });

  describe('validateMessage', () => {
    it('validates correct messages', () => {
      expect(validateMessage('This is a valid message that is long enough.')).toBe(true);
    });

    it('rejects invalid messages', () => {
      expect(validateMessage('')).toBe(false);
      expect(validateMessage('Short')).toBe(false); // Too short
      expect(validateMessage('A'.repeat(5001))).toBe(false); // Too long
    });
  });

  describe('escapeRegExp', () => {
    it('escapes special regex characters', () => {
      expect(escapeRegExp('.*+?^${}()|[]')).toBe('\\.\\*\\+\\?\\^\\$\\{\\}\\(\\)\\|\\[\\]');
    });

    it('handles normal strings', () => {
      expect(escapeRegExp('hello world')).toBe('hello world');
    });
  });

  describe('generateCSRFToken', () => {
    it('generates a token of correct length', () => {
      const token = generateCSRFToken();
      expect(token).toHaveLength(32);
    });

    it('generates different tokens on each call', () => {
      const token1 = generateCSRFToken();
      const token2 = generateCSRFToken();
      expect(token1).not.toBe(token2);
    });
  });

  describe('validateCSRFToken', () => {
    it('validates correct tokens', () => {
      const token = generateCSRFToken();
      expect(validateCSRFToken(token, token)).toBe(true);
    });

    it('rejects different tokens', () => {
      const token1 = generateCSRFToken();
      const token2 = generateCSRFToken();
      expect(validateCSRFToken(token1, token2)).toBe(false);
    });

    it('rejects empty tokens', () => {
      expect(validateCSRFToken('', '')).toBe(false);
      expect(validateCSRFToken('valid', '')).toBe(false);
    });
  });

  describe('hashString', () => {
    it('generates consistent hashes for same input', async () => {
      const input = 'test string';
      const hash1 = await hashString(input);
      const hash2 = await hashString(input);
      expect(hash1).toBe(hash2);
    });

    it('generates different hashes for different inputs', async () => {
      const hash1 = await hashString('input1');
      const hash2 = await hashString('input2');
      expect(hash1).not.toBe(hash2);
    });
  });

  describe('isValidURL', () => {
    it('validates correct URLs', () => {
      expect(isValidURL('https://example.com')).toBe(true);
      expect(isValidURL('http://localhost:3000')).toBe(true);
    });

    it('rejects invalid URLs', () => {
      expect(isValidURL('not-a-url')).toBe(false);
      expect(isValidURL('ftp://example.com')).toBe(false);
    });
  });
});
