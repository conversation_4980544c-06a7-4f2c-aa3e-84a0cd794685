#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔒 Generating enhanced security headers for GitHub Pages...');

const headers = `
# Enhanced Security Headers for GitHub Pages
/*
  # Core Security Headers
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  
  # Enhanced Permissions Policy
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(), picture-in-picture=()
  
  # HSTS for HTTPS enforcement
  Strict-Transport-Security: max-age=63072000; includeSubDomains; preload
  
  # Enhanced Content Security Policy
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://formspree.io https://www.google-analytics.com https://vercel.live https://va.vercel-scripts.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://formspree.io https://www.google-analytics.com https://vitals.vercel-insights.com; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self' https://formspree.io; upgrade-insecure-requests; block-all-mixed-content
  
  # Additional Security Headers
  Cross-Origin-Embedder-Policy: require-corp
  Cross-Origin-Opener-Policy: same-origin
  Cross-Origin-Resource-Policy: same-origin

# Enhanced Cache Control for Performance
/_next/static/*
  Cache-Control: public, max-age=31536000, immutable
  X-Content-Type-Options: nosniff

/images/*
  Cache-Control: public, max-age=31536000, stale-while-revalidate=86400
  X-Content-Type-Options: nosniff

/*.css
  Cache-Control: public, max-age=31536000, immutable
  X-Content-Type-Options: nosniff

/*.js
  Cache-Control: public, max-age=31536000, immutable
  X-Content-Type-Options: nosniff

/*.woff2
  Cache-Control: public, max-age=31536000, immutable
  X-Content-Type-Options: nosniff

/*.woff
  Cache-Control: public, max-age=31536000, immutable
  X-Content-Type-Options: nosniff

/*.webp
  Cache-Control: public, max-age=31536000, stale-while-revalidate=86400
  X-Content-Type-Options: nosniff

/*.avif
  Cache-Control: public, max-age=31536000, stale-while-revalidate=86400
  X-Content-Type-Options: nosniff

/*.svg
  Cache-Control: public, max-age=31536000, stale-while-revalidate=86400
  X-Content-Type-Options: nosniff

# HTML files with shorter cache for updates
/*.html
  Cache-Control: public, max-age=3600, stale-while-revalidate=86400
  X-Content-Type-Options: nosniff
`;

try {
  // Ensure out directory exists
  const outDir = path.join(process.cwd(), 'out');
  if (!fs.existsSync(outDir)) {
    fs.mkdirSync(outDir, { recursive: true });
  }

  // Write _headers file for GitHub Pages
  const headersPath = path.join(outDir, '_headers');
  fs.writeFileSync(headersPath, headers.trim());
  
  console.log('✅ Security headers generated successfully');
  console.log(`📁 Headers file created at: ${headersPath}`);
  
} catch (error) {
  console.error('❌ Error generating security headers:', error);
  process.exit(1);
}
