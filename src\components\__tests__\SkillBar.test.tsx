import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import SkillBar from '../SkillBar';

describe('SkillBar', () => {
  const defaultProps = {
    name: 'React',
    level: 85,
    colorClass: 'blue-purple',
  };

  it('renders skill name and level', () => {
    render(<SkillBar {...defaultProps} />);
    
    expect(screen.getByText('React')).toBeInTheDocument();
    expect(screen.getByText('85%')).toBeInTheDocument();
  });

  it('applies correct color class to progress bar', () => {
    render(<SkillBar {...defaultProps} />);
    
    const progressBar = document.querySelector('.skill-progress-bar');
    expect(progressBar).toHaveClass('blue-purple');
  });

  it('sets initial width to 0%', () => {
    render(<SkillBar {...defaultProps} />);
    
    const progressBar = document.querySelector('.skill-progress-bar');
    expect(progressBar).toHaveStyle('width: 0%');
  });

  it('renders with different skill levels', () => {
    const { rerender } = render(<SkillBar {...defaultProps} level={90} />);
    expect(screen.getByText('90%')).toBeInTheDocument();

    rerender(<SkillBar {...defaultProps} level={75} />);
    expect(screen.getByText('75%')).toBeInTheDocument();
  });

  it('renders with different color classes', () => {
    const { rerender } = render(<SkillBar {...defaultProps} colorClass="orange-red" />);
    
    let progressBar = document.querySelector('.skill-progress-bar');
    expect(progressBar).toHaveClass('orange-red');

    rerender(<SkillBar {...defaultProps} colorClass="green-teal" />);
    progressBar = document.querySelector('.skill-progress-bar');
    expect(progressBar).toHaveClass('green-teal');
  });

  it('handles long skill names', () => {
    render(<SkillBar {...defaultProps} name="Very Long Skill Name That Might Wrap" />);
    
    expect(screen.getByText('Very Long Skill Name That Might Wrap')).toBeInTheDocument();
  });

  it('handles edge case levels', () => {
    const { rerender } = render(<SkillBar {...defaultProps} level={0} />);
    expect(screen.getByText('0%')).toBeInTheDocument();

    rerender(<SkillBar {...defaultProps} level={100} />);
    expect(screen.getByText('100%')).toBeInTheDocument();
  });
});
