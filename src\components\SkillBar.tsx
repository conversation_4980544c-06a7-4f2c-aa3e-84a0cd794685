'use client';

import { useEffect, useRef } from 'react';

interface SkillBarProps {
  name: string;
  level: number;
  colorClass: string;
}

export default function SkillBar({ name, level, colorClass }: SkillBarProps) {
  const progressRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (progressRef.current) {
      // Use requestAnimationFrame for smooth animation
      requestAnimationFrame(() => {
        if (progressRef.current) {
          progressRef.current.style.setProperty('--skill-width', `${level}%`);
        }
      });
    }
  }, [level]);

  return (
    <div>
      <div className="flex justify-between text-xs text-gray-300 mb-1">
        <span>{name}</span>
        <span>{level}%</span>
      </div>
      <div className="w-full h-2 bg-slate-700 rounded-full overflow-hidden">
        <div
          ref={progressRef}
          className={`skill-progress-bar ${colorClass}`}
        ></div>
      </div>
    </div>
  );
}
