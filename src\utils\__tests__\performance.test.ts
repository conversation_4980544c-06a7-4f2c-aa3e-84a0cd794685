import { trackWebVitals, measurePerformance, debounce, throttle } from '../performance';

// Mock performance API
const mockPerformance = {
  now: jest.fn(() => 1000),
  mark: jest.fn(),
  measure: jest.fn(),
  getEntriesByType: jest.fn(() => []),
  getEntriesByName: jest.fn(() => []),
};

Object.defineProperty(global, 'performance', {
  value: mockPerformance,
  writable: true,
});

// Mock console methods
const originalConsole = { ...console };
beforeEach(() => {
  console.log = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
  jest.clearAllMocks();
});

afterEach(() => {
  Object.assign(console, originalConsole);
});

describe('performance utilities', () => {
  describe('trackWebVitals', () => {
    it('logs web vitals metrics', () => {
      const metric = {
        name: 'FCP',
        value: 1500,
        id: 'test-id',
        delta: 1500,
        entries: [],
      };

      trackWebVitals(metric);

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Web Vital'),
        expect.stringContaining('FCP'),
        expect.stringContaining('1500')
      );
    });

    it('handles different metric types', () => {
      const metrics = [
        { name: 'LCP', value: 2500, id: 'lcp-id', delta: 2500, entries: [] },
        { name: 'FID', value: 100, id: 'fid-id', delta: 100, entries: [] },
        { name: 'CLS', value: 0.1, id: 'cls-id', delta: 0.1, entries: [] },
      ];

      metrics.forEach(metric => trackWebVitals(metric));

      expect(console.log).toHaveBeenCalledTimes(3);
    });
  });

  describe('measurePerformance', () => {
    it('measures synchronous function performance', () => {
      const testFn = jest.fn(() => 'result');
      
      const result = measurePerformance('test-operation', testFn);

      expect(result).toBe('result');
      expect(testFn).toHaveBeenCalledTimes(1);
      expect(mockPerformance.mark).toHaveBeenCalledWith('test-operation-start');
      expect(mockPerformance.mark).toHaveBeenCalledWith('test-operation-end');
      expect(mockPerformance.measure).toHaveBeenCalledWith(
        'test-operation',
        'test-operation-start',
        'test-operation-end'
      );
    });

    it('measures asynchronous function performance', async () => {
      const testFn = jest.fn(async () => 'async-result');
      
      const result = await measurePerformance('async-operation', testFn);

      expect(result).toBe('async-result');
      expect(testFn).toHaveBeenCalledTimes(1);
    });

    it('handles function errors gracefully', () => {
      const errorFn = jest.fn(() => {
        throw new Error('Test error');
      });

      expect(() => measurePerformance('error-operation', errorFn)).toThrow('Test error');
      expect(mockPerformance.mark).toHaveBeenCalledWith('error-operation-start');
      expect(mockPerformance.mark).toHaveBeenCalledWith('error-operation-end');
    });
  });

  describe('debounce', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('delays function execution', () => {
      const fn = jest.fn();
      const debouncedFn = debounce(fn, 100);

      debouncedFn();
      expect(fn).not.toHaveBeenCalled();

      jest.advanceTimersByTime(100);
      expect(fn).toHaveBeenCalledTimes(1);
    });

    it('cancels previous calls when called multiple times', () => {
      const fn = jest.fn();
      const debouncedFn = debounce(fn, 100);

      debouncedFn();
      debouncedFn();
      debouncedFn();

      jest.advanceTimersByTime(100);
      expect(fn).toHaveBeenCalledTimes(1);
    });

    it('passes arguments to the debounced function', () => {
      const fn = jest.fn();
      const debouncedFn = debounce(fn, 100);

      debouncedFn('arg1', 'arg2');
      jest.advanceTimersByTime(100);

      expect(fn).toHaveBeenCalledWith('arg1', 'arg2');
    });
  });

  describe('throttle', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('limits function execution frequency', () => {
      const fn = jest.fn();
      const throttledFn = throttle(fn, 100);

      throttledFn();
      throttledFn();
      throttledFn();

      expect(fn).toHaveBeenCalledTimes(1);

      jest.advanceTimersByTime(100);
      throttledFn();

      expect(fn).toHaveBeenCalledTimes(2);
    });

    it('executes immediately on first call', () => {
      const fn = jest.fn();
      const throttledFn = throttle(fn, 100);

      throttledFn();
      expect(fn).toHaveBeenCalledTimes(1);
    });
  });
});
