{"name": "Saran - Portfolio", "short_name": "Saran Portfolio", "description": "Full Stack Developer & Software Engineer portfolio showcasing modern web applications and space technology projects", "start_url": "/web/", "display": "standalone", "background_color": "#0f172a", "theme_color": "#8b5cf6", "orientation": "portrait-primary", "scope": "/web/", "lang": "en", "dir": "ltr", "categories": ["portfolio", "developer", "technology"], "icons": [{"src": "/web/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "any maskable"}, {"src": "/web/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "any maskable"}, {"src": "/web/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "any maskable"}, {"src": "/web/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "any maskable"}, {"src": "/web/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "any maskable"}, {"src": "/web/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/web/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "any maskable"}, {"src": "/web/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}], "screenshots": [{"src": "/web/screenshot-desktop.png", "sizes": "1280x720", "type": "image/png", "platform": "wide", "label": "Desktop view of <PERSON><PERSON>'s portfolio"}, {"src": "/web/screenshot-mobile.png", "sizes": "390x844", "type": "image/png", "platform": "narrow", "label": "Mobile view of <PERSON><PERSON>'s portfolio"}], "shortcuts": [{"name": "About", "short_name": "About", "description": "Learn more about <PERSON><PERSON>", "url": "/web/#about", "icons": [{"src": "/web/icon-96x96.png", "sizes": "96x96"}]}, {"name": "Projects", "short_name": "Projects", "description": "View portfolio projects", "url": "/web/#projects", "icons": [{"src": "/web/icon-96x96.png", "sizes": "96x96"}]}, {"name": "Contact", "short_name": "Contact", "description": "Get in touch", "url": "/web/#contact", "icons": [{"src": "/web/icon-96x96.png", "sizes": "96x96"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}